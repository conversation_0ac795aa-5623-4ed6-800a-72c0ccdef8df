Here’s a clean, copy-paste **AGENTS.md** you can drop into your repo. It’s tailored to the **minimal CLI** version (OpenRouter + Playwright, no server, no Docker).

---

# AGENTS.md — Simple CLI Competitive & Features Analysis

## Purpose

Build and run a **single-user Python CLI** that:

1. Reads multi-line user input from STDIN.
2. Uses **OpenRouter** to (a) extract key requirements, then (b) generate a competitive & product-features analysis.
3. Uses **Playwright** to capture screenshots from real product pages (pricing/docs/demo).
4. Writes a **Markdown report** to `/reports` and saves images to `/screenshots`.

No servers, no Docker, no CI/CD.

---

## Operating constraints (important)

* **Interface:** CLI only — `python3 app.py` then user pastes text, end with EOF.
* **LLM access:** OpenRouter API (Chat Completions). Use environment variable `OPENROUTER_API_KEY`.
* **Libraries:** `requests` for HTTP, `playwright` for screenshots.
* **Compliance:** Only screenshot pages the user provides or explicitly approves. Respect site TOS/robots. Mask/avoid PII.
* **Outputs:** Professional, deterministic file names (see “Naming”).

---

## Inputs & Outputs

**Input (from user):**

* Free-form, multi-line brief describing industry, competitors, features of interest, and any known URLs.

**Outputs (files):**

* `/reports/{YYYY-MM-DD}-{industry_slug}-competitive-features-analysis.md`
* `/screenshots/{YYYY-MM-DD}_{domain}_{label}_{HH-MM-SS}.png` (+ optional selector clips)

---

## Directory layout (minimal)

```
competitor_cli/
  app.py          # CLI orchestrator
  llm.py          # OpenRouter calls (extract + analyze)
  shots.py        # Playwright helpers
  requirements.txt
  README.md
  reports/        # output
  screenshots/    # output
```

---

## Environment variables

* `OPENROUTER_API_KEY` (required)
* `OPENROUTER_MODEL` (optional, default: `anthropic/claude-3.5-sonnet`)
* `OPENROUTER_HTTP_REFERER` (optional; helps OpenRouter analytics)
* `OPENROUTER_X_TITLE` (optional; helps OpenRouter analytics)

---

## Setup

```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
playwright install
export OPENROUTER_API_KEY=sk-or-...
```

`requirements.txt`

```
requests>=2.32
playwright>=1.45
```

---

## Workflow (agent steps)

1. **Collect multi-line input**

   * Read from STDIN until EOF.
   * Persist raw prompt in memory (no DB needed).

2. **Extract requirements (OpenRouter → JSON)**

   * System prompt: strict extractor.
   * User prompt: user text + schema description.
   * If JSON parsing fails, **retry once** with “Return JSON only.”

3. **Screenshot pass (Playwright)**

   * For each `target_urls` item (`url`, `label`, `selectors[]`):

     * Navigate with `wait_until="networkidle"`, `page.screenshot(full_page=True)`.
     * For each selector present: `locator(sel).first.screenshot()`.
   * Save to `/screenshots` using naming rule.
   * Collect saved paths for the report.

4. **Write analysis (OpenRouter → Markdown)**

   * System prompt: product strategy analyst; concise, decision-ready.
   * User content: Requirements JSON + small “Evidence stub” (shot paths + URLs).
   * Output: a complete **Markdown** report.

5. **Emit files**

   * Write the Markdown to `/reports` with the **naming convention**.
   * Print both locations to stdout.

---

## Minimal schemas (for extraction)

**Requirements JSON**

```json
{
  "industry": "string",
  "geos": ["EU","US"],
  "time_range": {"from":"YYYY","to":"YYYY"},
  "competitors": ["string"],
  "features_of_interest": ["string"],
  "target_urls": [
    {"url":"https://...", "label":"pricing|docs|demo", "selectors":["#plans",".pricing-table"]}
  ],
  "notes": "optional context"
}
```

---

## Prompts (compact)

### A) Extract requirements

**system**
“You are a structured extractor. Output **only** valid JSON for the schema I describe. If unknown, use an empty string/array; do not invent companies or links.”

**user**

```
{user_multiline_text}

Schema:
Return ONLY JSON with keys:
industry, geos, time_range, competitors, features_of_interest, target_urls, notes.
target_urls is an array of objects: { "url": "...", "label": "pricing|docs|demo", "selectors": ["#sel", ".cls"] }.
```

### B) Write competitive & features analysis (Markdown)

**system**
“You are a product strategy analyst. Write a **concise, decision-ready Markdown** report comparing competitors & features. If facts are unknown, say ‘unknown’ (do not invent).”

**user**

````
Requirements JSON:
```json
{requirements_json}
````

Evidence stub:

```json
{"screenshots": [... absolute or relative paths ...]}
```

Write sections:

1. Executive summary
2. Competitor overview (table)
3. Feature comparison (bullets by feature; parity vs differentiation)
4. Pricing & packaging signals (if known)
5. Risks & unknowns
6. Suggested next screenshots

````

---

## OpenRouter call (reference snippet)

```python
# llm.py
import os, requests, json
URL = "https://openrouter.ai/api/v1/chat/completions"
HEADERS = {
  "Authorization": f"Bearer {os.environ['OPENROUTER_API_KEY']}",
  "Content-Type": "application/json",
  "HTTP-Referer": os.getenv("OPENROUTER_HTTP_REFERER",""),
  "X-Title": os.getenv("OPENROUTER_X_TITLE","Personal CLI"),
}
MODEL = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")

def chat(messages, temperature=0.2):
    resp = requests.post(URL, headers=HEADERS, json={"model": MODEL, "messages": messages, "temperature": temperature}, timeout=60)
    resp.raise_for_status()
    return resp.json()["choices"][0]["message"]["content"]
````

---

## Screenshot rules (Playwright)

* **Full page:** `page.screenshot(full_page=True)`
* **Element clips:** `locator(sel).first.screenshot()` when present
* **Waits:** `wait_until="networkidle"`, then `wait_for_timeout(800)` ms
* **Viewport:** 1440×900 default
* **Masking (optional):** if adding later, use `mask=[locator1,...]` for PII

---

## Naming conventions (must follow)

* **Report**
  `reports/{YYYY-MM-DD}-{industry_slug}-competitive-features-analysis.md`

  * `industry_slug` = lowercased, non-alphanumeric → `-`, trimmed.

* **Screenshots**
  `screenshots/{YYYY-MM-DD}_{domain}_{label}_{HH-MM-SS}.png`

  * `domain` from URL host; `label` sanitized (alnum + `-`/`_`).

---

## Error handling

* If extraction returns non-JSON → **retry once** with “Return JSON only.”
* If any screenshot selector is missing → skip selector clip, still keep full-page.
* Network errors → print a short note and continue remaining targets.
* Never crash on a single bad URL.

---

## Guardrails

* Only screenshot URLs provided or confirmed by the user.
* Respect robots.txt and site TOS; avoid authenticated/private areas.
* Don’t capture personal dashboards or PII; if unavoidable, do not save.
* The model must not fabricate prices/features; allow “unknown”.

---

## Acceptance checklist (done when all pass)

* Running `python3 app.py` produces:

  * A valid **Requirements JSON** (printed or logged),
  * At least one **full-page screenshot** per target URL,
  * A **Markdown report** with sections listed above, saved under `/reports`.
* Filenames match the **naming conventions**.
* If the user provides selectors, at least one **element clip** is attempted per URL.
* The report includes a **Suggested next screenshots** checklist.

---

## Quick usage

```bash
python3 app.py
# Paste multi-line brief, e.g.:
# Compare my ELN device-control vs Benchling & IDBS for EU/US this year.
# Focus on device-control, audit trails, permissions.
# Include screenshots of pricing pages & docs.
# (Then press Ctrl+D)

# Output paths printed:
# reports/2025-08-17-eln-competitive-features-analysis.md
# screenshots/2025-08-17_benchling.com_pricing_14-32-05.png
```

---

## Small backlog (still simple)

* Add a tiny `selectors.json` with common pricing/docs selectors.
* Optional: “URL proposer” LLM step that suggests 3–5 pages per competitor for you to approve.
* Optional: prepend a table of screenshots (filename → short description) to the report.

---