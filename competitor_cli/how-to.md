# How to Use the Competitive Analysis CLI
### 🚀 Usage

1. **Setup**:
   ```bash
   cd competitor_cli
   pip install -r requirements.txt
   playwright install
   export OPENROUTER_API_KEY=sk-or-your-key-here
   ```

2. **Run**:
   ```bash
   python3 app.py
   # Paste your analysis request, then Ctrl+D
   ```

3. **Example input** (see `example_input.txt`):
   ```
   Compare my ELN platform vs Benchling & IDBS for EU/US markets.
   Focus on device-control, audit trails, permissions.
   Include screenshots of pricing pages.
   ```

### 📊 Output Files

- **Reports**: `reports/2025-08-17-electronic-lab-notebook-competitive-features-analysis.md`
- **Screenshots**: `screenshots/2025-08-17_benchling.com_pricing_14-32-05.png`