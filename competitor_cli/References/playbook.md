Got it—here’s a **pared-down playbook** tailored to your new constraints: a **simple Python CLI app** (run with `python3 app.py`) that 1) extracts key requirements from multi-line input using **OpenRouter**, 2) asks the LLM to perform a deep competitive & feature analysis, and 3) captures **Playwright** screenshots of real product UIs, then writes a **.md report** to `/reports` and images to `/screenshots`. No servers, no Docker, no CI—just a tidy script and a couple helpers.

---

# Minimal CLI Playbook

## 1) What we’re building (single-user, local)

* **app.py** — CLI entrypoint: reads multi-line prompt → orchestrates steps → saves outputs.
* **llm.py** — Thin OpenRouter client (requests-based) with two calls:

  * `extract_requirements(prompt)` → compact JSON (industry, competitors, features, urls to shoot, etc.)
  * `write_analysis(requirements, evidence_stub)` → polished competitive & features analysis (Markdown).
* **shots.py** — Playwright helpers to capture full-page and element screenshots.
* **/reports** — Markdown reports, professionally named.
* **/screenshots** — Images named by timestamp + domain + short label.

> OpenRouter specifics you’ll use: `POST https://openrouter.ai/api/v1/chat/completions` with `Authorization: Bearer <OPENROUTER_API_KEY>` and optional `HTTP-Referer` + `X-Title` headers; request body mirrors OpenAI chat format (`model`, `messages`). ([OpenRouter][1])
> For available models (e.g., `anthropic/claude-3.5-sonnet`, `openai/gpt-4o-mini`), see models docs. ([OpenRouter][2])
> Playwright screenshots: `page.screenshot(full_page=True)` and `locator.screenshot()` for components; optional `mask` to hide PII. ([Playwright][3])

---

## 2) File tree & naming conventions (professional)

```
competitor_cli/
  app.py
  llm.py
  shots.py
  requirements.txt
  README.md
  reports/              # out
  screenshots/          # out
```

**Naming rules**

* Reports: `reports/{date}-{industry}-competitive-features-analysis.md`
  Example: `reports/2025-08-17-eln-competitive-features-analysis.md`
* Screens: `screenshots/{date}_{domain}_{label}_{hhmmss}.png`
  Example: `screenshots/2025-08-17_benchling.com_pricing_143205.png`

---

## 3) Dependencies & setup

**requirements.txt**

```
requests>=2.32
playwright>=1.45
```

Setup:

```bash
python -m venv .venv && source .venv/bin/activate
pip install -r requirements.txt
playwright install
export OPENROUTER_API_KEY=sk-or-...
# optional, but recommended for OpenRouter analytics
export OPENROUTER_HTTP_REFERER="http://localhost/local-cli"
export OPENROUTER_X_TITLE="Personal CLI"
```

(OpenRouter auth headers & chat completion endpoint. ([OpenRouter][1]))

---

## 4) Minimal schemas (what we ask the LLM to return)

**Requirements JSON** (keep it tiny and focused on your use case)

```json
{
  "industry": "string",
  "geos": ["EU","US"],
  "time_range": {"from":"YYYY","to":"YYYY"},
  "competitors": ["string"],
  "features_of_interest": ["string"],
  "target_urls": [
    {"url":"https://...", "label":"pricing", "selectors":["#plans",".pricing-table"]}
  ],
  "notes": "optional context"
}
```

---

## 5) Prompts (short and strict)

**A) Extract requirements**
*System*: “You are a structured extractor. Output **only** valid JSON for the schema I describe next. If unknown, use an empty array/string; do not invent companies or links.”
*User*: (your multi-line text) + the schema above inline.

**B) Write competitive & features analysis**
*System*: “You are a product strategy analyst. Write a **concise Markdown report** comparing competitors and features. Include an ‘Assumptions & Gaps’ section and a ‘Suggested Next Screenshots’ checklist. Do not fabricate prices or claims—if unsure, say ‘unknown’.”
*User*: Provide the Requirements JSON + any URLs already known.

---

## 6) Orchestration flow (inside `app.py`)

1. **Read multi-line input** until EOF (Ctrl+D on mac/linux; Ctrl+Z then Enter on Windows).
2. **Call OpenRouter** → `extract_requirements()` to get the compact JSON. ([OpenRouter][4])
3. **Screenshot pass** using `shots.py` over `requirements["target_urls"]`. (Add or edit URLs after the first run if needed.)
4. **Call OpenRouter** → `write_analysis()` with the requirements + a tiny evidence stub (list of URLs you shot).
5. **Write** Markdown to `/reports`, embed a table of screenshots and link files stored under `/screenshots`.

---

## 7) Code sketches (copy-ready)

**app.py**

```python
import os, sys, json, datetime, pathlib
from llm import extract_requirements, write_analysis
from shots import shoot_all

BASE = pathlib.Path(__file__).resolve().parent
REPORTS = BASE / "reports"; REPORTS.mkdir(exist_ok=True)
SHOTS = BASE / "screenshots"; SHOTS.mkdir(exist_ok=True)

def read_multiline():
    print("Paste your request. End with EOF (Ctrl+D) on *nix / Ctrl+Z then Enter on Windows.\n---")
    return sys.stdin.read().strip()

def professional_slug(s: str) -> str:
    return "".join(c.lower() if c.isalnum() else "-" for c in s).strip("-")

def main():
    prompt = read_multiline()
    req = extract_requirements(prompt)           # dict with keys shown in schema
    today = datetime.date.today().isoformat()
    industry_slug = professional_slug(req.get("industry","industry"))
    # 1) screenshots
    shots = shoot_all(req.get("target_urls", []), out_dir=str(SHOTS))
    # 2) analysis
    report_md = write_analysis(req, evidence={"screenshots": shots})
    # 3) save report
    out = REPORTS / f"{today}-{industry_slug}-competitive-features-analysis.md"
    with open(out, "w", encoding="utf-8") as f:
        f.write(report_md)
    print(f"\nReport: {out}\nScreenshots: {SHOTS}")

if __name__ == "__main__":
    main()
```

**llm.py** (OpenRouter chat completions via `requests`)

````python
import os, requests, json, textwrap

OPENROUTER_API_KEY = os.environ["OPENROUTER_API_KEY"]
OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL = os.getenv("OPENROUTER_MODEL", "anthropic/claude-3.5-sonnet")  # pick your favorite
HEADERS = {
    "Authorization": f"Bearer {OPENROUTER_API_KEY}",
    "Content-Type": "application/json",
    # optional but recommended:
    "HTTP-Referer": os.getenv("OPENROUTER_HTTP_REFERER", ""),
    "X-Title": os.getenv("OPENROUTER_X_TITLE", "Personal CLI"),
}

REQ_SCHEMA = textwrap.dedent("""\
Return ONLY JSON with keys:
industry, geos, time_range, competitors, features_of_interest, target_urls, notes.
target_urls is an array of objects: { "url": "...", "label": "pricing|docs|demo", "selectors": ["#sel", ".cls"] }.
""")

def _chat(messages, temperature=0.2):
    body = {"model": MODEL, "messages": messages, "temperature": temperature}
    r = requests.post(OPENROUTER_URL, headers=HEADERS, json=body, timeout=60)
    r.raise_for_status()
    data = r.json()
    return data["choices"][0]["message"]["content"]  # OpenRouter matches OpenAI schema. :contentReference[oaicite:5]{index=5}

def extract_requirements(user_text: str) -> dict:
    sys_prompt = ("You are a structured extractor. " +
                  "Output ONLY valid JSON per the requested keys. If unknown, use empty arrays/strings.")
    user_prompt = f"{user_text}\n\nSchema:\n{REQ_SCHEMA}"
    content = _chat([{"role": "system", "content": sys_prompt},
                     {"role": "user", "content": user_prompt}])
    try:
        return json.loads(content)
    except json.JSONDecodeError:
        # one retry asking for JSON only
        content = _chat([{"role": "system", "content": sys_prompt},
                         {"role": "user", "content": "Return JSON only. " + REQ_SCHEMA}])
        return json.loads(content)

def write_analysis(requirements: dict, evidence: dict) -> str:
    sys_prompt = ("You are a product strategy analyst. "
                  "Write a concise, decision-ready Markdown report for competitive & feature analysis. "
                  "If a fact is unknown, state 'unknown'—do not invent numbers.")
    user_prompt = f"""Requirements JSON:
```json
{json.dumps(requirements, ensure_ascii=False, indent=2)}
````

Evidence stub:

```json
{json.dumps(evidence, ensure_ascii=False, indent=2)}
```

Write sections:

1. Executive summary
2. Competitor overview (table)
3. Feature comparison (bullets by feature; note parity vs differentiation)
4. Pricing & packaging signals (if known)
5. Risks & unknowns
6. Suggested next screenshots (URLs/paths)
   """
   md = \_chat(\[{"role": "system", "content": sys\_prompt},
   {"role": "user", "content": user\_prompt}], temperature=0.3)
   return md

````
(OpenRouter authentication & chat completion flow. :contentReference[oaicite:6]{index=6})

**shots.py** (Playwright sync API)
```python
from playwright.sync_api import sync_playwright
import os, time, hashlib

def _safe_label(label: str) -> str:
    return "".join(c if c.isalnum() or c in "-_" else "-" for c in label)[:60]

def shoot_all(targets, out_dir="screenshots", width=1440, height=900, wait_ms=800):
    os.makedirs(out_dir, exist_ok=True)
    saved = []
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        ctx = browser.new_context(viewport={"width": width, "height": height})
        page = ctx.new_page()
        for t in targets:
            url = t.get("url"); label = t.get("label") or url.split("//")[-1].split("/")[0]
            selectors = t.get("selectors") or []
            page.goto(url, wait_until="networkidle")
            if wait_ms: page.wait_for_timeout(wait_ms)
            ts = time.strftime("%Y-%m-%d_%H-%M-%S")
            base = f"{ts}_{_safe_label(label)}"
            full_path = os.path.join(out_dir, f"{base}.png")
            page.screenshot(path=full_path, full_page=True)     # full page shot. :contentReference[oaicite:7]{index=7}
            saved.append(full_path)
            for sel in selectors:
                loc = page.locator(sel).first
                if loc.count() > 0:
                    h = hashlib.sha1(sel.encode()).hexdigest()[:8]
                    pth = os.path.join(out_dir, f"{base}_{h}.png")
                    loc.screenshot(path=pth)                    # element clip. :contentReference[oaicite:8]{index=8}
                    saved.append(pth)
        ctx.close(); browser.close()
    return saved
````

---

## 8) Usage

```bash
python3 app.py
# paste your multi-line brief, e.g.:
# compare my ELN device-control vs Benchling & IDBS for EU/US this year.
# focus on device-control, audit trails, permissions.
# include screenshots of pricing pages & docs.
# (then press Ctrl+D)

# outputs:
# reports/2025-08-17-eln-competitive-features-analysis.md
# screenshots/2025-08-17_benchling.com_pricing_143205.png ...
```

---

## 9) Guardrails (lightweight, still important)

* **Robots/TOS**: only point the screenshotter at pages you’re allowed to capture; throttle if needed. (The CLI doesn’t crawl—only visits URLs you provide/approve.)
* **PII masking**: use Playwright’s `mask` if you later extend to hide user info. ([Playwright][5])
* **Model choice**: start with a dependable generalist (e.g., Claude 3.5 Sonnet, GPT-4o-mini). See OpenRouter docs for availability & endpoints. ([OpenRouter][2])

---

## 10) Small backlog (keep it simple)

* **Structured output hardening**: add `instructor` with OpenRouter for type-safe JSON if you want fewer parse retries. ([python.useinstructor.com][6])
* **Selector presets**: keep a tiny JSON with common pricing/docs selectors you can append to requirements.
* **Auto-URL discover** (optional): one extra LLM call that proposes 5 URLs per competitor; you approve/edit before screenshots.
* **Screenshot index in report**: append a table with relative paths and labels.

---

[1]: https://openrouter.ai/docs/api-reference/authentication?utm_source=chatgpt.com "API Authentication | OpenRouter OAuth and API Keys"
[2]: https://openrouter.ai/docs/api-reference/list-available-models?utm_source=chatgpt.com "List available models | OpenRouter | Documentation"
[3]: https://playwright.dev/python/docs/screenshots?utm_source=chatgpt.com "Screenshots | Playwright Python"
[4]: https://openrouter.ai/docs/quickstart?utm_source=chatgpt.com "OpenRouter Quickstart Guide | Developer Documentation"
[5]: https://playwright.dev/python/docs/api/class-page?utm_source=chatgpt.com "Page | Playwright Python"
[6]: https://python.useinstructor.com/integrations/openrouter/?utm_source=chatgpt.com "Structured outputs with OpenRouter, a complete guide with ..."
