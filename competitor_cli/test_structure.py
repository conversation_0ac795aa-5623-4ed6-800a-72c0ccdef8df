#!/usr/bin/env python3
"""
Test script to validate the competitive analysis CLI structure.
This can run without API keys to verify the basic setup.
"""

import os
import sys
import json
from pathlib import Path

def test_file_structure():
    """Test that all required files exist."""
    print("🔍 Testing file structure...")
    
    required_files = [
        "app.py",
        "llm.py", 
        "shots.py",
        "requirements.txt",
        "README.md"
    ]
    
    required_dirs = [
        "reports",
        "screenshots"
    ]
    
    all_good = True
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} missing")
            all_good = False
    
    for dir in required_dirs:
        if os.path.exists(dir) and os.path.isdir(dir):
            print(f"✅ {dir}/")
        else:
            print(f"❌ {dir}/ missing")
            all_good = False
    
    return all_good

def test_helper_functions():
    """Test helper functions that don't require API keys."""
    print("\n🧪 Testing helper functions...")
    
    try:
        # Test shots.py helpers
        from shots import _safe_label, _extract_domain
        
        # Test cases
        test_cases = [
            ("pricing & plans", "pricing---plans"),
            ("Documentation", "documentation"),
            ("API Docs", "api-docs"),
            ("", "page")
        ]
        
        for input_val, expected in test_cases:
            result = _safe_label(input_val)
            if result == expected:
                print(f"✅ _safe_label('{input_val}') = '{result}'")
            else:
                print(f"❌ _safe_label('{input_val}') = '{result}', expected '{expected}'")
        
        # Test domain extraction
        domain_cases = [
            ("https://www.example.com/pricing", "example.com"),
            ("https://benchling.com/docs", "benchling.com"),
            ("http://subdomain.test.org/path", "subdomain.test.org"),
            ("invalid-url", "unknown-domain")
        ]
        
        for url, expected in domain_cases:
            result = _extract_domain(url)
            if result == expected:
                print(f"✅ _extract_domain('{url}') = '{result}'")
            else:
                print(f"❌ _extract_domain('{url}') = '{result}', expected '{expected}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Helper function test failed: {e}")
        return False

def test_requirements_schema():
    """Test that the requirements schema is well-formed."""
    print("\n📋 Testing requirements schema...")
    
    # Example valid requirements JSON
    example_req = {
        "industry": "Electronic Lab Notebooks",
        "geos": ["EU", "US"],
        "time_range": {"from": "2024", "to": "2025"},
        "competitors": ["Benchling", "IDBS", "LabArchives"],
        "features_of_interest": ["device-control", "audit-trails", "permissions"],
        "target_urls": [
            {
                "url": "https://benchling.com/pricing",
                "label": "pricing",
                "selectors": ["#plans", ".pricing-table"]
            }
        ],
        "notes": "Focus on enterprise features"
    }
    
    try:
        # Test JSON serialization
        json_str = json.dumps(example_req, indent=2)
        parsed = json.loads(json_str)
        
        # Validate required keys
        required_keys = ["industry", "geos", "competitors", "features_of_interest", "target_urls"]
        for key in required_keys:
            if key in parsed:
                print(f"✅ Schema has '{key}' field")
            else:
                print(f"❌ Schema missing '{key}' field")
                return False
        
        # Validate target_urls structure
        if parsed["target_urls"] and isinstance(parsed["target_urls"], list):
            url_obj = parsed["target_urls"][0]
            url_keys = ["url", "label", "selectors"]
            for key in url_keys:
                if key in url_obj:
                    print(f"✅ URL object has '{key}' field")
                else:
                    print(f"❌ URL object missing '{key}' field")
        
        print("✅ Requirements schema validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Schema test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Competitive Analysis CLI - Structure Test")
    print("=" * 50)
    
    tests = [
        ("File Structure", test_file_structure),
        ("Helper Functions", test_helper_functions), 
        ("Requirements Schema", test_requirements_schema)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 All tests passed! The CLI structure is ready.")
        print("\nNext steps:")
        print("1. Set OPENROUTER_API_KEY environment variable")
        print("2. Run: pip install -r requirements.txt")
        print("3. Run: playwright install")
        print("4. Test with: python3 app.py")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
