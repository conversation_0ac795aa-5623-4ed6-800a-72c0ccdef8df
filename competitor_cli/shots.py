"""
Playwright screenshot helpers for competitive analysis.
Captures full-page and element screenshots from target URLs.
"""

import os
import time
import hashlib
from urllib.parse import urlparse
from playwright.sync_api import sync_playwright


def _safe_label(label: str) -> str:
    """Convert label to safe filename component."""
    if not label:
        return "page"
    return "".join(c.lower() if c.isalnum() or c in "-_" else "-" for c in label)[:60]


def _extract_domain(url: str) -> str:
    """Extract domain from URL for filename."""
    try:
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        # Remove www. prefix if present
        if domain.startswith('www.'):
            domain = domain[4:]
        return domain if domain else "unknown-domain"
    except Exception:
        return "unknown-domain"


def shoot_all(targets, out_dir="screenshots", width=1440, height=900, wait_ms=800):
    """
    Take screenshots of all target URLs using <PERSON>wright.
    
    Args:
        targets: List of dicts with 'url', 'label', and optional 'selectors'
        out_dir: Output directory for screenshots
        width: Browser viewport width
        height: Browser viewport height  
        wait_ms: Milliseconds to wait after page load
    
    Returns:
        List of saved screenshot file paths
    """
    if not targets:
        return []
    
    os.makedirs(out_dir, exist_ok=True)
    saved = []
    
    try:
        with sync_playwright() as p:
            # Launch browser
            browser = p.chromium.launch(headless=True)
            context = browser.new_context(viewport={"width": width, "height": height})
            page = context.new_page()
            
            for target in targets:
                try:
                    url = target.get("url")
                    if not url:
                        print(f"⚠️  Skipping target with no URL: {target}")
                        continue
                    
                    label = target.get("label") or _extract_domain(url)
                    selectors = target.get("selectors") or []
                    
                    print(f"📸 Capturing: {url}")
                    
                    # Navigate to page
                    page.goto(url, wait_until="networkidle", timeout=30000)
                    
                    # Wait for page to settle
                    if wait_ms > 0:
                        page.wait_for_timeout(wait_ms)
                    
                    # Generate timestamp and base filename
                    timestamp = time.strftime("%Y-%m-%d_%H-%M-%S")
                    domain = _extract_domain(url)
                    safe_label = _safe_label(label)
                    base_filename = f"{timestamp}_{domain}_{safe_label}"
                    
                    # Take full-page screenshot
                    full_path = os.path.join(out_dir, f"{base_filename}.png")
                    page.screenshot(path=full_path, full_page=True)
                    saved.append(full_path)
                    print(f"  ✅ Full page: {os.path.basename(full_path)}")
                    
                    # Take element screenshots for each selector
                    for selector in selectors:
                        try:
                            locator = page.locator(selector).first
                            if locator.count() > 0:
                                # Create unique filename for this selector
                                selector_hash = hashlib.sha1(selector.encode()).hexdigest()[:8]
                                element_path = os.path.join(out_dir, f"{base_filename}_{selector_hash}.png")
                                locator.screenshot(path=element_path)
                                saved.append(element_path)
                                print(f"  ✅ Element ({selector}): {os.path.basename(element_path)}")
                            else:
                                print(f"  ⚠️  Selector not found: {selector}")
                        except Exception as e:
                            print(f"  ⚠️  Failed to capture selector '{selector}': {e}")
                    
                except Exception as e:
                    print(f"❌ Failed to capture {target.get('url', 'unknown URL')}: {e}")
                    continue
            
            # Clean up
            context.close()
            browser.close()
            
    except Exception as e:
        print(f"❌ Playwright error: {e}")
        raise
    
    return saved
