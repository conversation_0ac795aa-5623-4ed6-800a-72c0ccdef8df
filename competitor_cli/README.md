# Competitive Analysis CLI

A minimal Python CLI tool that uses <PERSON>Rout<PERSON> and <PERSON><PERSON> to generate competitive analysis reports with screenshots.

## Features

- **Multi-line input**: Paste your analysis request and end with EOF
- **AI-powered extraction**: Uses OpenRouter to extract structured requirements
- **Screenshot capture**: Takes full-page and element screenshots using Playwright
- **Professional reports**: Generates Markdown reports with competitive analysis
- **Clean file organization**: Saves reports and screenshots with professional naming

## Setup

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   playwright install
   ```

2. **Set environment variables**:

   **Option A: Create a .env file** (recommended):
   ```bash
   # Create .env file in the competitor_cli directory
   echo "OPENROUTER_API_KEY=sk-or-your-api-key-here" > .env
   echo "OPENROUTER_MODEL=anthropic/claude-3.5-sonnet" >> .env
   echo "OPENROUTER_HTTP_REFERER=http://localhost/local-cli" >> .env
   echo "OPENROUTER_X_TITLE=Competitive Analysis CLI" >> .env
   ```

   **Option B: Export environment variables**:
   ```bash
   export OPENROUTER_API_KEY=sk-or-your-api-key-here
   export OPENROUTER_MODEL=anthropic/claude-3.5-sonnet
   export OPENROUTER_HTTP_REFERER=http://localhost/local-cli
   export OPENROUTER_X_TITLE="Competitive Analysis CLI"
   ```

## Usage

```bash
python3 app.py
```

Then paste your competitive analysis request, for example:

```
Compare my ELN device-control features vs Benchling & IDBS for EU/US markets this year.
Focus on device-control, audit trails, and permissions.
Include screenshots of their pricing pages and documentation.
```

End your input with EOF:
- **Unix/Mac**: Press `Ctrl+D`
- **Windows**: Press `Ctrl+Z` then `Enter`

## Output Files

The tool creates:

- **Reports**: `reports/YYYY-MM-DD-industry-competitive-features-analysis.md`
- **Screenshots**: `screenshots/YYYY-MM-DD_domain_label_HH-MM-SS.png`

## Example Input Format

The AI will extract structured requirements from your free-form text, looking for:

- Industry/market context
- Geographic regions (EU, US, etc.)
- Time range for analysis
- Competitor names
- Features of interest
- URLs to screenshot (pricing, docs, demos)

## Requirements Schema

The AI extracts information into this JSON structure:

```json
{
  "industry": "string",
  "geos": ["EU", "US"],
  "time_range": {"from": "YYYY", "to": "YYYY"},
  "competitors": ["string"],
  "features_of_interest": ["string"],
  "target_urls": [
    {
      "url": "https://...",
      "label": "pricing|docs|demo",
      "selectors": ["#plans", ".pricing-table"]
    }
  ],
  "notes": "optional context"
}
```

## Report Sections

Generated reports include:

1. Executive summary
2. Competitor overview (table)
3. Feature comparison (parity vs differentiation)
4. Pricing & packaging signals
5. Risks & unknowns
6. Suggested next screenshots

## Error Handling

- Retries JSON parsing if initial extraction fails
- Continues with remaining URLs if one screenshot fails
- Graceful handling of missing CSS selectors
- Clear error messages for setup issues

## Dependencies

- `requests>=2.32` - For OpenRouter API calls
- `playwright>=1.45` - For web screenshots
- `python-dotenv>=1.0.0` - For loading .env files

## Environment Variables

- `OPENROUTER_API_KEY` (required) - Your OpenRouter API key
- `OPENROUTER_MODEL` (optional) - Model to use (default: anthropic/claude-3.5-sonnet)
- `OPENROUTER_HTTP_REFERER` (optional) - For OpenRouter analytics
- `OPENROUTER_X_TITLE` (optional) - For OpenRouter analytics
