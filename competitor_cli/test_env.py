#!/usr/bin/env python3
"""
Test script to verify .env file loading and basic app functionality.
"""

import os
import sys
from pathlib import Path

def test_env_loading():
    """Test that environment variables are loaded from .env file."""
    print("🔍 Testing .env file loading...")
    
    # Check if .env file exists
    env_file = Path('.env')
    if env_file.exists():
        print(f"✅ .env file found: {env_file.absolute()}")
        
        # Read and display (safely) the contents
        with open(env_file, 'r') as f:
            lines = f.readlines()
        
        print("📄 .env file contents:")
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                key = line.split('=')[0]
                if 'API_KEY' in key:
                    print(f"   {key}=***hidden***")
                else:
                    print(f"   {line}")
    else:
        print("❌ .env file not found")
        return False
    
    # Test loading with dotenv
    try:
        from dotenv import load_dotenv
        load_dotenv()
        print("✅ python-dotenv loaded successfully")
    except ImportError:
        print("❌ python-dotenv not installed")
        return False
    
    # Check if API key is available
    api_key = os.environ.get('OPENROUTER_API_KEY')
    if api_key:
        print(f"✅ OPENROUTER_API_KEY loaded (starts with: {api_key[:10]}...)")
    else:
        print("❌ OPENROUTER_API_KEY not found in environment")
        return False
    
    return True

def test_module_imports():
    """Test that all modules can be imported."""
    print("\n🧪 Testing module imports...")
    
    modules = ['app', 'llm', 'shots']
    success = True
    
    for module_name in modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name}.py imports successfully")
        except Exception as e:
            print(f"❌ {module_name}.py import failed: {e}")
            success = False
    
    return success

def test_helper_functions():
    """Test helper functions work correctly."""
    print("\n🔧 Testing helper functions...")
    
    try:
        from app import professional_slug
        from shots import _safe_label, _extract_domain
        
        # Test professional_slug
        test_cases = [
            ("Electronic Lab Notebooks", "electronic-lab-notebooks"),
            ("API & Integration", "api---integration"),
            ("", "industry")
        ]
        
        for input_val, expected in test_cases:
            result = professional_slug(input_val)
            if result == expected:
                print(f"✅ professional_slug('{input_val}') = '{result}'")
            else:
                print(f"❌ professional_slug('{input_val}') = '{result}', expected '{expected}'")
        
        # Test screenshot helpers
        label_result = _safe_label("Pricing & Plans")
        domain_result = _extract_domain("https://benchling.com/pricing")
        
        print(f"✅ _safe_label('Pricing & Plans') = '{label_result}'")
        print(f"✅ _extract_domain('https://benchling.com/pricing') = '{domain_result}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Helper function test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("🚀 Environment & Module Test")
    print("=" * 40)
    
    tests = [
        ("Environment Loading", test_env_loading),
        ("Module Imports", test_module_imports),
        ("Helper Functions", test_helper_functions)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if not passed:
            all_passed = False
    
    print("=" * 40)
    if all_passed:
        print("🎉 All tests passed! The app is ready to use.")
        print("\n🚀 To run the competitive analysis:")
        print("   python3 app.py")
        print("\n📝 Then paste your analysis request and press Ctrl+D")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    sys.exit(main())
