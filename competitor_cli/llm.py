"""
OpenRouter API client for competitive analysis.
Handles requirement extraction and analysis generation.
"""

import os
import requests
import json
import textwrap
from pathlib import Path

# Load environment variables from .env file if it exists
try:
    from dotenv import load_dotenv
    # Look for .env file in the same directory as this script
    env_path = Path(__file__).parent / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✅ Loaded environment variables from {env_path}")
    else:
        # Try loading from current directory
        load_dotenv()
except ImportError:
    print("⚠️  python-dotenv not installed. Install with: pip install python-dotenv")

# Environment variables
OPENROUTER_API_KEY = os.environ.get("OPENROUTER_API_KEY")
if not OPENROUTER_API_KEY:
    raise ValueError("OPENROUTER_API_KEY environment variable is required. Please set it in your .env file or environment.")

OPENROUTER_URL = "https://openrouter.ai/api/v1/chat/completions"
MODEL = os.getenv("OPENROUTER_MODEL", "anthropic/claude-sonnet-4")

# Headers for OpenRouter API
HEADERS = {
    "Authorization": f"Bearer {OPENROUTER_API_KEY}",
    "Content-Type": "application/json",
    # Optional but recommended for OpenRouter analytics
    "HTTP-Referer": os.getenv("OPENROUTER_HTTP_REFERER", "http://localhost/local-cli"),
    "X-Title": os.getenv("OPENROUTER_X_TITLE", "Competitive Analysis CLI"),
}

# Schema description for requirements extraction
REQ_SCHEMA = textwrap.dedent("""\
Return ONLY JSON with keys:
industry, geos, time_range, competitors, features_of_interest, target_urls, notes.
target_urls is an array of objects: { "url": "...", "label": "pricing|docs|demo", "selectors": ["#sel", ".cls"] }.
""")


def _chat(messages, temperature=0.2):
    """Make a chat completion request to OpenRouter."""
    body = {
        "model": MODEL,
        "messages": messages,
        "temperature": temperature
    }
    
    try:
        response = requests.post(OPENROUTER_URL, headers=HEADERS, json=body, timeout=60)
        response.raise_for_status()
        data = response.json()
        return data["choices"][0]["message"]["content"]
    except requests.exceptions.RequestException as e:
        raise Exception(f"OpenRouter API error: {e}")
    except (KeyError, IndexError) as e:
        raise Exception(f"Unexpected OpenRouter response format: {e}")


def extract_requirements(user_text: str) -> dict:
    """
    Extract structured requirements from user text using OpenRouter.
    Returns a dictionary with industry, competitors, features, URLs, etc.
    """
    sys_prompt = (
        "You are a structured extractor. "
        "Output ONLY valid JSON per the requested keys. "
        "If unknown, use empty arrays/strings. "
        "Do not invent companies or links."
    )
    
    user_prompt = f"{user_text}\n\nSchema:\n{REQ_SCHEMA}"
    
    messages = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": user_prompt}
    ]
    
    try:
        content = _chat(messages)
        return json.loads(content)
    except json.JSONDecodeError:
        # Retry once with explicit JSON-only instruction
        retry_messages = [
            {"role": "system", "content": sys_prompt},
            {"role": "user", "content": f"Return JSON only. {REQ_SCHEMA}"}
        ]
        content = _chat(retry_messages)
        try:
            return json.loads(content)
        except json.JSONDecodeError as e:
            raise Exception(f"Failed to parse JSON after retry: {e}")


def write_analysis(requirements: dict, evidence: dict) -> str:
    """
    Generate a competitive analysis report using OpenRouter.
    Returns a Markdown-formatted report.
    """
    sys_prompt = (
        "You are a product strategy analyst. "
        "Write a concise, decision-ready Markdown report for competitive & feature analysis. "
        "If a fact is unknown, state 'unknown'—do not invent numbers or claims."
    )
    
    user_prompt = f"""Requirements JSON:
```json
{json.dumps(requirements, ensure_ascii=False, indent=2)}
```

Evidence stub:
```json
{json.dumps(evidence, ensure_ascii=False, indent=2)}
```

Write sections:

1. Executive summary
2. Competitor overview (table)
3. Feature comparison (bullets by feature; note parity vs differentiation)
4. Pricing & packaging signals (if known)
5. Risks & unknowns
6. Suggested next screenshots (URLs/paths)
"""
    
    messages = [
        {"role": "system", "content": sys_prompt},
        {"role": "user", "content": user_prompt}
    ]
    
    return _chat(messages, temperature=0.3)
