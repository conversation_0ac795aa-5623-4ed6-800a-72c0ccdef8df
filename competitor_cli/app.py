#!/usr/bin/env python3
"""
CLI orchestrator for competitive analysis tool.
Reads multi-line input, extracts requirements, takes screenshots, generates analysis.
"""

import os
import sys
import json
import datetime
import pathlib
from llm import extract_requirements, write_analysis
from shots import shoot_all

# Setup paths
BASE = pathlib.Path(__file__).resolve().parent
REPORTS = BASE / "reports"
SHOTS = BASE / "screenshots"

# Ensure output directories exist
REPORTS.mkdir(exist_ok=True)
SHOTS.mkdir(exist_ok=True)


def read_multiline():
    """Read multi-line input from STDIN until EOF."""
    print("Paste your competitive analysis request below.")
    print("End with EOF (Ctrl+D on Unix/Mac, Ctrl+Z then Enter on Windows).")
    print("---")
    try:
        return sys.stdin.read().strip()
    except KeyboardInterrupt:
        print("\nOperation cancelled.")
        sys.exit(1)


def professional_slug(s: str) -> str:
    """Convert string to professional filename slug."""
    if not s:
        return "industry"
    return "".join(c.lower() if c.isalnum() else "-" for c in s).strip("-")[:50]


def main():
    """Main orchestration function."""
    try:
        # Step 1: Read user input
        print("🔍 Competitive Analysis CLI")
        print("=" * 40)
        prompt = read_multiline()
        
        if not prompt:
            print("No input provided. Exiting.")
            return
        
        print(f"\n📝 Processing {len(prompt)} characters of input...")
        
        # Step 2: Extract requirements using OpenRouter
        print("🤖 Extracting requirements...")
        try:
            req = extract_requirements(prompt)
            print(f"✅ Requirements extracted for industry: {req.get('industry', 'unknown')}")
        except Exception as e:
            print(f"❌ Failed to extract requirements: {e}")
            return
        
        # Step 3: Generate file names
        today = datetime.date.today().isoformat()
        industry_slug = professional_slug(req.get("industry", "industry"))
        
        # Step 4: Take screenshots
        target_urls = req.get("target_urls", [])
        if target_urls:
            print(f"📸 Taking screenshots of {len(target_urls)} URLs...")
            try:
                shots = shoot_all(target_urls, out_dir=str(SHOTS))
                print(f"✅ Captured {len(shots)} screenshots")
            except Exception as e:
                print(f"⚠️  Screenshot error: {e}")
                shots = []
        else:
            print("ℹ️  No URLs provided for screenshots")
            shots = []
        
        # Step 5: Generate analysis
        print("📊 Generating competitive analysis...")
        try:
            evidence = {"screenshots": shots}
            report_md = write_analysis(req, evidence)
            print("✅ Analysis generated")
        except Exception as e:
            print(f"❌ Failed to generate analysis: {e}")
            return
        
        # Step 6: Save report
        report_filename = f"{today}-{industry_slug}-competitive-features-analysis.md"
        report_path = REPORTS / report_filename
        
        try:
            with open(report_path, "w", encoding="utf-8") as f:
                f.write(report_md)
            print(f"✅ Report saved: {report_path}")
        except Exception as e:
            print(f"❌ Failed to save report: {e}")
            return
        
        # Step 7: Print summary
        print("\n" + "=" * 40)
        print("🎉 Analysis Complete!")
        print(f"📄 Report: {report_path}")
        print(f"📸 Screenshots: {SHOTS}")
        if shots:
            print(f"   - {len(shots)} images captured")
        print("=" * 40)
        
    except KeyboardInterrupt:
        print("\n\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
